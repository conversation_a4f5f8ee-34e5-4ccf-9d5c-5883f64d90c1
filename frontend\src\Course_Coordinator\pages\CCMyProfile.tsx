import { useState } from "react";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import defaultProfilePic from "../../assets/logo.png";

const profileData = {
  name: "<PERSON><PERSON> <PERSON><PERSON><PERSON>",
  id: "F2020IT001",
  department: "Computer Engineering",
  role: "Course Coordinator",
  phone: "9876543210",
  email: "<EMAIL>",
  academicYear: "2025 - 2026",
};

const CCMyProfile = () => {
  const [showModal, setShowModal] = useState(false);

  const handleResetClick = () => {
    setShowModal(true);
  };

  const handleConfirmReset = () => {
    setShowModal(false);
    alert("Password reset request sent to admin.");
  };

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="coursecoordinator" />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar role="coursecoordinator" />
        </div>

        <div className="flex-1 p-6 bg-gray-50 overflow-auto">
          <h1 className="text-[30px] font-bold text-[#ED1C24] mb-10 text-center">My Profile</h1>

          <div className="bg-white rounded-lg shadow p-7 relative">
            <div className="flex items-start justify-between flex-wrap gap-4">
              <div className="flex gap-4 items-center">
                <img src={defaultProfilePic} alt="Profile" className="w-16 h-16 rounded-full" />
                <div>
                  <h2 className="text-xl font-semibold mb-1">{profileData.name}</h2>
                  <p className="text-sm text-gray-600">Department: {profileData.department}</p>
                </div>
              </div>

              <div className="text-sm text-gray-600 text-right font-medium">
                Academic Year: <span className="text-[#ED1C24]"> {profileData.academicYear}</span>
              </div>
            </div>

            <div className="text-sm pt-6 space-y-2">
              <p>
                <strong>ID:</strong> {profileData.id}
              </p>
              <p>
                <strong>Role:</strong> {profileData.role}
              </p>
              <p>
                <strong>Phone:</strong> {profileData.phone}
              </p>
              <p>
                <strong>Email ID:</strong> {profileData.email}
              </p>
            </div>

            {/* Reset Password Button */}
            <div className="flex justify-end">
              <button
                onClick={handleResetClick}
                className="px-4 py-2 bg-red-700 text-white font-medium rounded hover:bg-red-800 transition text-sm"
              >
                Reset Password
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-40 flex items-center justify-center">
          <div className="bg-white p-6 rounded-md shadow-lg max-w-md w-full text-center">
            <h2 className="text-lg font-semibold text-[#231F20] mb-6">
              Are you sure you want to send a request to admin to reset your password?
            </h2>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-1.5 bg-gray-300 text-[#231F20] rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmReset}
                className="px-4 py-1.5 bg-[#ED1C24] text-white rounded hover:bg-[#c41a1f]"
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CCMyProfile;
